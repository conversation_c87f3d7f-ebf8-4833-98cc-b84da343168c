import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// Validate environment variables
const requiredEnvVars = [
  'VITE_SUPABASE_URL',
  'VITE_SUPABASE_ANON_KEY'
] as const;

const missingEnvVars = requiredEnvVars.filter(
  varName => !import.meta.env[varName]
);

if (missingEnvVars.length > 0) {
  throw new Error(`Missing required Supabase environment variables: ${missingEnvVars.join(', ')}`);
}

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Supabase URL and Anon Key are required');
}

// Debugging: Log Supabase URL and Anon Key
if (import.meta.env.DEV) {
  console.log('Supabase URL:', supabaseUrl);
  console.log('Supabase Anon Key:', supabaseAnonKey ? 'Loaded' : 'Not Loaded');
}

// Create Supabase client with enhanced auth configuration
const edgeFunctionUrl = `${supabaseUrl}/functions/v1/cors-proxy`;

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    storage: window.localStorage,
    storageKey: 'supabase.auth.token',
    debug: import.meta.env.DEV
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  },
  global: {
    headers: {
      'X-Client-Info': 'isotopeai-web'
    },
    fetch: (input, init) => {
      const url = new URL(input.toString());
      if (url.pathname.startsWith('/rest/v1')) {
        const newUrl = `${edgeFunctionUrl}${url.pathname.replace('/rest/v1', '')}${url.search}`;
        return fetch(newUrl, init);
      }
      return fetch(input, init);
    },
  }
});

// Add auth state change listener for debugging
if (import.meta.env.DEV) {
  supabase.auth.onAuthStateChange((event, session) => {
    console.log('Auth state changed:', event, session?.user?.id);
  });
}
