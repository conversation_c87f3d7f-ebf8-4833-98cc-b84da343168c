import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const SUPABASE_URL = Deno.env.get("SUPABASE_URL")
const SUPABASE_ANON_KEY = Deno.env.get("SUPABASE_ANON_KEY")

serve(async (req) => {
  const url = new URL(req.url)
  const path = url.pathname
  const search = url.search

  // Handle preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PATCH, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers": "authorization, content-type, apikey",
      },
    })
  }

  const targetUrl = `${SUPABASE_URL}/rest/v1${path}${search}`

  const fwdHeaders = new Headers(req.headers)
  fwdHeaders.set('apikey', SUPABASE_ANON_KEY!)

  const res = await fetch(targetUrl, {
    method: req.method,
    headers: fwdHeaders,
    body: req.body,
  })

  const responseHeaders = new Headers(res.headers)
  responseHeaders.set("Access-Control-Allow-Origin", "*")
  responseHeaders.set("Access-Control-Allow-Methods", "GET, POST, PATCH, PUT, DELETE, OPTIONS")
  responseHeaders.set("Access-Control-Allow-Headers", "authorization, content-type, apikey")

  return new Response(res.body, {
    status: res.status,
    headers: responseHeaders,
  })
})
